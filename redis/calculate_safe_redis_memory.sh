#!/bin/bash
# calculate_safe_redis_memory.sh

echo "=== Redis内存安全配置计算 ==="

TOTAL_MEM=968  # MB
USED_MEM=209   # MB (系统基础使用)
AVAILABLE_MEM=438  # MB

echo "系统内存分析："
echo "总内存: ${TOTAL_MEM}MB"
echo "系统基础使用: ${USED_MEM}MB"
echo "当前可用: ${AVAILABLE_MEM}MB"
echo "缓存(可释放): 339MB"

echo
echo "Redis内存配置建议："

# 保守配置 (50%可用内存)
CONSERVATIVE=$((AVAILABLE_MEM * 50 / 100))
echo "保守配置: ${CONSERVATIVE}MB (50%可用内存)"

# 平衡配置 (70%可用内存)
BALANCED=$((AVAILABLE_MEM * 70 / 100))
echo "平衡配置: ${BALANCED}MB (70%可用内存)"

# 激进配置 (80%可用内存)
AGGRESSIVE=$((AVAILABLE_MEM * 80 / 100))
echo "激进配置: ${AGGRESSIVE}MB (80%可用内存)"

echo
echo "推荐配置命令："
echo "redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 SET maxmemory ${BALANCED}mb"

echo
echo "当前Redis配置检查："
export REDISCLI_AUTH=wocloud
CURRENT_MAXMEM=$(redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 GET maxmemory 2>/dev/null)
if [ ! -z "$CURRENT_MAXMEM" ]; then
    CURRENT_MB=$((CURRENT_MAXMEM / 1024 / 1024))
    echo "当前maxmemory: ${CURRENT_MB}MB"
    
    if [ $CURRENT_MB -gt $AVAILABLE_MEM ]; then
        echo "⚠️  警告: 当前设置超过可用内存!"
    elif [ $CURRENT_MB -gt $AGGRESSIVE ]; then
        echo "⚠️  注意: 当前设置较为激进"
    else
        echo "✅ 当前设置相对安全"
    fi
fi
