#!/bin/bash
# analyze_system_memory.sh

echo "=== 系统内存详细分析 ==="

echo "1. 基础内存信息："
free -h

echo
echo "2. 详细内存分类 (/proc/meminfo)："
cat /proc/meminfo | grep -E "(MemTotal|MemFree|MemAvailable|Buffers|Cached|SReclaimable|Slab)"

echo
echo "3. 缓存详细构成："
echo "Buffers (磁盘缓冲区): $(cat /proc/meminfo | grep Buffers | awk '{print $2/1024}')MB"
echo "Cached (页面缓存): $(cat /proc/meminfo | grep -w Cached | awk '{print $2/1024}')MB"
echo "SReclaimable (可回收Slab): $(cat /proc/meminfo | grep SReclaimable | awk '{print $2/1024}')MB"

echo
echo "4. 进程内存使用TOP10："
ps aux --sort=-%mem | head -11

echo
echo "5. Redis进程内存使用："
ps aux | grep redis | grep -v grep

echo
echo "6. 系统负载和运行时间："
uptime

echo
echo "7. 磁盘IO统计 (可能影响缓存)："
iostat -x 1 1 2>/dev/null || echo "iostat未安装"
