#!/bin/bash
# redis-evict-detail.sh

export REDISCLI_AUTH=wocloud

echo "=== Redis内存淘汰测试配置 ==="

# 1. maxmemory配置选择
echo "选择maxmemory大小："
echo "1) 10MB (快速测试)"
echo "2) 50MB (中等测试)"
echo "3) 100MB (大容量测试)"
echo "4) 自定义"
read -p "请选择 (1-4): " MEMORY_CHOICE

case $MEMORY_CHOICE in
    1) MAXMEMORY="10mb" ;;
    2) MAXMEMORY="50mb" ;;
    3) MAXMEMORY="100mb" ;;
    4)
        read -p "请输入自定义大小(如: 200mb): " MAXMEMORY
        ;;
    *) MAXMEMORY="10mb" ;;
esac

# 2. 淘汰策略选择
echo
echo "选择淘汰策略："
echo "1) allkeys-lru (可淘汰任何key)"
echo "2) volatile-lru (只淘汰有TTL的key)"
read -p "请选择 (1/2): " POLICY_CHOICE

case $POLICY_CHOICE in
    1)
        POLICY="allkeys-lru"
        WRITE_METHOD="normal"
        ;;
    2)
        POLICY="volatile-lru"
        WRITE_METHOD="with_ttl"
        ;;
    *)
        POLICY="allkeys-lru"
        WRITE_METHOD="normal"
        ;;
esac

echo
echo "=== 配置确认 ==="
echo "maxmemory: $MAXMEMORY"
echo "策略: $POLICY"
echo "写入方式: $WRITE_METHOD"
echo

# 设置Redis配置
echo "设置Redis配置..."
redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 SET maxmemory $MAXMEMORY
redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 SET maxmemory-policy $POLICY
redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 SET maxmemory-samples 5
redis-cli FLUSHALL

# 验证配置
echo "当前配置："
echo "maxmemory: $(redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 GET maxmemory)"
echo "policy: $(redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 GET maxmemory-policy)"

# 写入函数定义
write_normal_keys() {
    local count=$1
    local size=$2
    local keyspace=$3

    redis-benchmark -a wocloud -t set -n $count -d $size -r $keyspace -q > /dev/null
}

write_ttl_keys() {
    local count=$1
    local size=$2
    local keyspace=$3
    local ttl=${4:-3600}

    local data=$(head -c $size /dev/zero | tr '\0' 'a')

    for i in $(seq 1 $count); do
        local key="ttl_key_${RANDOM}_${i}"
        redis-cli SETEX "$key" $ttl "$data" > /dev/null

        if [ $((i % 100)) -eq 0 ]; then
            printf "\r进度: ${i}/${count}"
        fi
    done
    echo
}

monitor_memory_status() {
    local batch=$1

    # 获取内存信息
    MEMORY_INFO=$(redis-cli INFO memory)
    USED=$(echo "$MEMORY_INFO" | grep "used_memory:" | cut -d: -f2 | tr -d '\r')
    USED_DATASET=$(echo "$MEMORY_INFO" | grep "used_memory_dataset:" | cut -d: -f2 | tr -d '\r')
    USED_OVERHEAD=$(echo "$MEMORY_INFO" | grep "used_memory_overhead:" | cut -d: -f2 | tr -d '\r')

    USED_MB=$((USED / 1024 / 1024))
    DATASET_MB=$((USED_DATASET / 1024 / 1024))
    OVERHEAD_MB=$((USED_OVERHEAD / 1024 / 1024))

    # 获取统计信息
    STATS_INFO=$(redis-cli INFO stats)
    EVICTED=$(echo "$STATS_INFO" | grep "evicted_keys:" | cut -d: -f2 | tr -d '\r')
    KEYS=$(redis-cli DBSIZE)

    if [ -z "$EVICTED" ]; then
        EVICTED=0
    fi

    # volatile-lru特有的监控
    if [ "$WRITE_METHOD" = "with_ttl" ]; then
        # 统计有TTL的key数量 (采样前100个key)
        TTL_KEYS=$(redis-cli --scan | head -100 | while read key; do
            TTL=$(redis-cli TTL "$key" 2>/dev/null)
            if [ "$TTL" -gt 0 ]; then
                echo "1"
            fi
        done | wc -l)

        echo "批次$batch: 总内存${USED_MB}MB, 数据集${DATASET_MB}MB, Keys:$KEYS, TTL_Keys:$TTL_KEYS, 淘汰:$EVICTED"
    else
        echo "批次$batch: 总内存${USED_MB}MB, 数据集${DATASET_MB}MB, 开销${OVERHEAD_MB}MB, Keys:$KEYS, 淘汰:$EVICTED"
    fi
}

echo
echo "开始测试..."

# 根据策略执行不同的测试流程
if [ "$WRITE_METHOD" = "normal" ]; then
    echo "=== allkeys-lru 测试流程 ==="

    for i in {1..50}; do
        write_normal_keys 1000 1024 100000
        monitor_memory_status $i

        if [ "$EVICTED" -gt 0 ] 2>/dev/null; then
            echo "🔥 淘汰开始！继续观察..."
            break
        fi

        sleep 0.5
    done

else
    echo "=== volatile-lru 测试流程 ==="

    # 先写入一些永久key作为基础数据
    echo "写入基础永久key..."
    write_normal_keys 500 1024 1000

    for i in {1..50}; do
        write_ttl_keys 1000 1024 100000 3600
        monitor_memory_status $i

        if [ "$EVICTED" -gt 0 ] 2>/dev/null; then
            echo "🔥 淘汰开始！继续观察..."
            break
        fi

        sleep 0.5
    done
fi

echo
echo "继续大量写入，观察稳定状态..."
for i in {1..20}; do
    if [ "$WRITE_METHOD" = "normal" ]; then
        write_normal_keys 5000 1024 1000000
    else
        write_ttl_keys 5000 1024 1000000 3600
    fi

    monitor_memory_status "稳定期$i"
    sleep 1
done

echo
echo "=== 测试结果分析 ==="
echo "配置信息："
echo "- maxmemory: $MAXMEMORY"
echo "- 策略: $POLICY"
echo "- 写入方式: $WRITE_METHOD"

echo
echo "=== 内存构成分析 ==="
redis-cli INFO memory | grep -E "(used_memory_human|used_memory_dataset_human|used_memory_overhead_human|maxmemory_human|mem_fragmentation_ratio)"

echo
echo "=== 统计信息 ==="
redis-cli INFO stats | grep evicted_keys
echo "最终key数量: $(redis-cli DBSIZE)"

echo
echo "=== 关键指标解读 ==="
MAXMEMORY_BYTES=$(redis-cli vV0Ai4mNx6eX3shyEj6p1EorzYQ5HMrsENg1 GET maxmemory | tail -1)
USED_DATASET_FINAL=$(redis-cli INFO memory | grep "used_memory_dataset:" | cut -d: -f2 | tr -d '\r')

# 确保变量不为空且为数字
if [ ! -z "$MAXMEMORY_BYTES" ] && [ ! -z "$USED_DATASET_FINAL" ] && [ "$MAXMEMORY_BYTES" -gt 0 ] 2>/dev/null; then
    DATASET_USAGE_PERCENT=$((USED_DATASET_FINAL * 100 / MAXMEMORY_BYTES))
    MAXMEMORY_MB=$((MAXMEMORY_BYTES / 1024 / 1024))
    DATASET_MB=$((USED_DATASET_FINAL / 1024 / 1024))

    echo "数据集内存使用率: ${DATASET_USAGE_PERCENT}%"
    echo "maxmemory限制: ${MAXMEMORY_MB}MB"
    echo "实际数据集内存: ${DATASET_MB}MB"
else
    echo "无法计算内存使用率 - 数据获取失败"
    echo "MAXMEMORY_BYTES: $MAXMEMORY_BYTES"
    echo "USED_DATASET_FINAL: $USED_DATASET_FINAL"
fi

# volatile-lru特有分析
if [ "$WRITE_METHOD" = "with_ttl" ]; then
    echo
    echo "=== volatile-lru 特有分析 ==="

    # 检查剩余key的TTL分布
    echo "剩余key的TTL分布 (采样前100个key):"
    redis-cli --scan | head -100 | while read key; do
        TTL=$(redis-cli TTL "$key" 2>/dev/null)
        if [ "$TTL" = "-1" ]; then
            echo "永久key"
        elif [ "$TTL" -gt 0 ]; then
            echo "TTL_key"
        fi
    done | sort | uniq -c

    echo
    echo "验证结果: volatile-lru应该优先淘汰TTL key，保留永久key"

    # 统计永久key数量
    PERMANENT_KEYS=$(redis-cli --scan | head -100 | while read key; do
        TTL=$(redis-cli TTL "$key" 2>/dev/null)
        if [ "$TTL" = "-1" ]; then
            echo "1"
        fi
    done | wc -l)

    echo "采样中永久key数量: $PERMANENT_KEYS/100"

    if [ "$PERMANENT_KEYS" -gt 50 ]; then
        echo "✅ volatile-lru工作正常: 永久key被保留"
    else
        echo "⚠️  需要检查: 永久key数量较少，可能配置有问题"
    fi
fi

echo
echo "=== 测试完成 ==="
