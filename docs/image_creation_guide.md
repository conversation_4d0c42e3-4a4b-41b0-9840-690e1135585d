# 联通云数据库镜像制作完整指南

## 目录
- [项目概述与架构](#项目概述与架构)
- [镜像制作流程详解](#镜像制作流程详解)
- [变更追踪与质量保证](#变更追踪与质量保证)
- [最佳实践与规范](#最佳实践与规范)

## 1. 项目概述与架构

### 1.1 业务背景

本项目负责为联通云数据库自动化镜像制作，包括MySQL、PostgreSQL、Redis、ClickHouse等，核心基于packer框架实现。

### 1.2 技术架构
```
镜像制作系统架构
├── Packer (镜像构建核心)
│   ├── HCL配置文件 (*.pkr.hcl)
│   ├── 环境配置文件 (env.conf)
│   ├── 构建脚本 (*_img_*.sh)
│   └── 系统参数脚本 (configure_*_param.sh)  # 构建脚本依赖
├── image-change-tracker (变更追踪)
│   ├── 镜像对比工具 (compare_images.sh)
│   ├── 变更记录工具 (record_image_changes.sh)
│   └── 历史查询工具 (query_image_history.sh)
└── 验证工具
    └── 镜像验证脚本 (check_*_image.sh)
```

### 1.3 支持的数据库类型

| 数据库 | 版本支持 | 架构支持 |
|--------|----------|----------|
| MySQL | 5.7.34, 8.0.21, 8.0.37 | x86_64, aarch64 |
| PostgreSQL | 13.3 | x86_64, aarch64 |
| Redis | 5.0.14, 7.0.15 | x86_64, aarch64 |
| ClickHouse | 22.5.1.2079 | x86_64, aarch64 |
| ClickHouseKeeper | 22.5.1 | x86_64, aarch64 |
| cudb | 2.0 | x86_64 |
| Cetus | 2.4 | x86_64, aarch64 |
| Predixy | 1.0.5 | x86_64, aarch64 |

### 1.4 镜像环境说明

#### 哈尔滨Poc08环境 (x86_64)
- **用途**: 主要生产环境，x86架构镜像制作
- **服务器地址**: ***********
- **操作系统**: culinux 3.0
- **代码位置**: /opt/image-packer/
- **YUM源地址**: [待补充]

**关键路径**:
```bash
# 基础镜像存储
ls /opt/packer-base-image/

# Trove包存储
ls /data/pip_bank/

# packer构建输出镜像
ls /opt/packer-images/pre-test

# 归档镜像版本存储（等价于硅谷ARM环境的/data/img-mgmt/workspace/archive/）
ls /data/mysql_bank/              # MySQL镜像归档
ls /data/postgresql_bank/         # PostgreSQL镜像归档
ls /data/redis_bank/              # Redis镜像归档
ls /data/clickhouse_bank/         # ClickHouse镜像归档
ls /data/cetus_bank/              # Cetus镜像归档
ls /data/predixy_bank/            # Predixy镜像归档
# 其他数据库的_bank目录...
```

#### 硅谷ARM环境 (aarch64)
- **用途**: ARM架构镜像制作
- **服务器地址**: ************
- **操作系统**: 银河麒麟 v10 sp3
- **代码位置**: /opt/image-packer/
- **YUM源地址**: [待补充]

**关键路径**:
```bash
# 基础镜像存储
ls /data/img-mgmt/base/

# Trove包存储
ls /data/img-mgmt/workspace/archive/pip_bank/

# Packer构建输出
ls /data/img-mgmt/workspace/working/packer/pre-test/

# 测试通过后的归档
ls /data/img-mgmt/workspace/archive/mysql_bank/
ls /data/img-mgmt/workspace/archive/postgresql13_bank/
ls /data/img-mgmt/workspace/archive/redis_bank/

# 发布版本管理
ls /data/img-mgmt/releases/          # 公有云大版本
ls /data/img-mgmt/projects/          # 私有云定制版本

# 镜像上传脚本配置文件
/data/wait_to_upload.img.list.zwl    # 镜像列表配置
/data/upload_image_zwl.sh            # 上传脚本（无入参）
```

**工作流程**:
1. Packer使用 base/ 目录的基础镜像
2. 构建输出到 working/packer/pre-test/
3. 通过编辑 /data/wait_to_upload.img.list.zwl 配置镜像，运行 /data/upload_image_zwl.sh 上传到Glance
4. Trove测试通过后归档到 workspace/archive/对应数据库_bank/
5. 公有云大版本发布时复制到 releases/
6. 私有云定制版本放到 projects/对应项目/

## 2. 镜像制作流程详解

### 2.1 标准制作流程

**硅谷ARM环境完整工作流程**：

1. **构建阶段**： Packer从 base/ 读取基础镜像，输出到 working/packer/pre-test/
2. **测试阶段**： 使用 upload_image_zwl.sh 上传到Glance，进行Trove功能测试
3. **归档阶段**： 测试通过后移动到 workspace/archive/对应数据库_bank/
4. **发布阶段**： 根据需要复制到 releases/ 或 projects/对应项目/

### 2.2 详细操作步骤

#### 步骤1: 需求确认
- 确定数据库类型和版本
- 确定目标架构 (x86_64/aarch64)
- 确定Trove版本升级需求
- 确定特殊配置需求

#### 步骤2: 环境准备
```bash
# 哈尔滨Poc08环境
cd /opt/image-packer
ls -la /opt/packer-base-image/          # 基础镜像
ls -la /data/pip_bank/trove-*.tar.gz    # Trove包
ls -la /opt/packer-images/pre-test      # 输出目录检查

# 硅谷ARM环境  
cd /opt/image-packer/                   # 代码目录
ls -la /data/img-mgmt/base/             # 基础镜像
ls -la /data/img-mgmt/workspace/archive/pip_bank/trove-*.tar.gz  # Trove包
ls -la /data/img-mgmt/workspace/working/packer/pre-test/         # 输出目录检查
```

#### 步骤3: 配置修改

以MySQL为例：

```bash
cd trove_db_image/mysql/

# 1. 修改数据库版本 (如需要)
vim mysql_img_x86.sh  # 或 mysql_img_arm.sh
export RDB_VERSION=5.7.34  # 修改为目标版本

# 2. 修改Trove版本 (如需要)
vim mysql-env_x86.conf  # 或 mysql-env_arm.conf
export MY_TROVE_TAG=********
export MY_TROVE_FILE_NAME=trove-10.5.0.dev4.tar.gz

# 3. 修改构建输出目录 (可选)
export OUTPUT_DIRECTORY=/opt/packer-images/pre-test  # x86
export OUTPUT_DIRECTORY=/data/img-mgmt/workspace/working/packer/pre-test/  # arm
```

#### 步骤4: 执行构建
```bash
# 设置SSH密码
export SSH_PASSWORD="your_password"

# 哈尔滨Poc08环境 (x86_64)
cd /opt/image-packer/trove_db_image/mysql/
sh mysql_img_x86.sh --author "zhaoj296" --description "MySQL 5.7.34 with Trove ********"

# 硅谷ARM环境 (aarch64)  
cd /opt/image-packer/trove_db_image/mysql/
sh mysql_img_arm.sh --author "zhaoj296" --description "MySQL 5.7.34 ARM版本"

# 检查构建输出
ls -la /data/img-mgmt/workspace/working/packer/pre-test/  # arm
ls -la /opt/packer-images/pre-test/   # x86

# 备用方案说明：
# 当Packer构建失败或出现问题时，可以使用virt-customize方式
# 详见附录C.3的备用方案说明
```

#### 步骤5: 镜像验证
```bash
# 基础验证
sh check_mysql_image.sh /data/img-mgmt/workspace/working/packer/pre-test/新镜像名.qcow2

# 手动验证关键配置
virt-cat /data/img-mgmt/workspace/working/packer/pre-test/新镜像名.qcow2 /etc/rc.local
virt-cat /data/img-mgmt/workspace/working/packer/pre-test/新镜像名.qcow2 /etc/my.cnf

# 上传到Glance并进行Trove功能测试
# 1. 编辑上传配置文件
vim /data/wait_to_upload.img.list.zwl

# 2. 在配置文件中添加或启用对应的镜像行，例如：
# mysql8037_image_name=新镜像名.qcow2
# 注意：去掉行首的#号来启用镜像上传

# 3. 执行上传到Glance
/data/upload_image_zwl.sh

# 4. 在Trove控制台进行功能测试
# - 创建数据库实例
# - 验证数据库服务正常启动
# - 测试基本的数据库操作

# Trove测试通过后，归档镜像
cp /data/img-mgmt/workspace/working/packer/pre-test/新镜像名.qcow2 \
   /data/img-mgmt/workspace/archive/mysql_bank/

# 根据发布需要，复制到对应目录：
# 公有云大版本发布：
# cp 新镜像名.qcow2 /data/img-mgmt/releases/版本号/

# 私有云定制版本：
# cp 新镜像名.qcow2 /data/img-mgmt/projects/项目名/
```

## 3. 变更追踪与质量保证

### 3.1 自动变更检测

使用image-change-tracker进行镜像对比：

```bash
cd image-change-tracker/

# 快速对比 (3-5分钟)
./bin/compare_images.sh \
  --base-image /opt/packer-base-image/base.qcow2 \
  --new-image /opt/packer-images/new.qcow2 \
  --config quick

# 标准对比 (8-12分钟)  
./bin/compare_images.sh \
  --base-image /opt/packer-base-image/base.qcow2 \
  --new-image /opt/packer-images/new.qcow2 \
  --config standard

# 生成变更记录
./bin/record_image_changes.sh \
  --base-image /opt/packer-base-image/base.qcow2 \
  --new-image /opt/packer-images/new.qcow2 \
  --author "zhaoj296" \
  --description "Trove版本升级到********"
```

### 3.2 变更记录管理
```bash
# 查询历史记录
./bin/query_image_history.sh --db-type mysql --db-version 5.7.34

# 查看变更详情（指定记录ID），记录ID可以通过上一步获取
./bin/query_image_history.sh --show-details "20250728_175529"
```

## 4. 最佳实践与规范

### 4.1 命名规范

#### 镜像命名规范
**格式**: `trove_{trove_version}_{db_type}{db_version}_{os}_{arch}_{date}.qcow2`

**示例**:
- `trove_********_mysql8.0.21_kyv10sp3_aarch64_20250728.qcow2`
- `trove_********_mysql5.7.34_culinux_x86_64_20250728.qcow2`
- `trove_********_postgresql13.3_kyv10sp3_aarch64_20250728.qcow2`
- `trove_********_redis5.0.14_kyv10sp3_aarch64_20250728.qcow2`
- `trove_********_clickhouse22.5.1.2079_kyv10sp3_aarch64_20250728.qcow2`

#### 基础镜像命名规范
**格式**: `trove_{db_type}{db_version}_{os}_{arch}_{date}_base.qcow2`

**示例**:
- `trove_mysql8.0.21_kyv10sp3_aarch64_2025-06-17_base.qcow2`
- `trove_mysql5.7.34_culinux_x86_64_2025-04-11_base.qcow2`
- `trove_postgresql13.3_kyv10sp3_aarch64_2025-05-28_base.qcow2`
- `trove_redis5.0.14_kyv10sp3_aarch64_2025-05-09_base.qcow2`
- `trove_clickhouse22.5.1.2079_kyv10sp3_aarch64_2025-05-28_base.qcow2`

### 4.2 安全规范

#### 防火墙配置
```bash
# 基础镜像中的防火墙规则
# 注意: base基础镜像中没有配置iptables规则，这是为了兼容Packer制作镜像时的SSH访问过程
# 基于base制作的Packer镜像都会添加以下iptables规则

# 允许特定网段访问Trove服务端口
iptables -A INPUT -p tcp --dport 59599 -s 100.0.0.0/8 -j ACCEPT
iptables -A INPUT -p tcp --dport 59599 -j DROP
```

## 附录

### A. 配置文件模板

#### A.1 MySQL环境配置模板
```bash
# mysql-env.conf
export RDB_VERSION=8.0.21
export MY_TROVE_TAG=********
export MY_TROVE_FILE_NAME=trove-10.5.0.dev4.tar.gz
export OUTPUT_DIRECTORY=/data/img-mgmt/workspace/working/packer/pre-test
export PACKER_BUILD_NAME=mysql-trove

# 基础镜像选择示例（硅谷ARM环境）
# 基础镜像路径: /data/img-mgmt/base/trove_mysql8.0.21_kyv10sp3_aarch64_2025-06-17_base.qcow2
```

#### A.2 基础镜像使用说明
```bash
# 查看可用的基础镜像
ls -la /data/img-mgmt/base/trove_*.qcow2

# 基础镜像命名说明：
# trove_mysql8.0.21_kyv10sp3_aarch64_2025-06-17_base.qcow2
#   ├── trove: 固定前缀
#   ├── mysql8.0.21: 数据库类型和版本（无下划线分隔）
#   ├── kyv10sp3: 操作系统版本
#   ├── aarch64: 处理器架构
#   ├── 2025-06-17: 制作日期
#   └── base: 基础镜像标识
```

#### A.3 镜像上传配置文件说明
```bash
# 编辑上传配置文件
vim /data/wait_to_upload.img.list.zwl

# 配置文件格式：
##image_name:镜像名称
#mysql_image_name=trove_10.5.0.7_mysql_5.7.34_kylinv10sp3_aarch64_20250728.qcow2
#mysql8021_image_name=trove_10.5.0.7_mysql_8.0.21_kylinv10sp3_aarch64_20250728.qcow2
mysql8037_image_name=trove_10.5.0.7_mysql_8.0.37_kylinv10sp3_aarch64_20250728.qcow2
#redis_image_name=trove_redis5.0.14_kyv10sp3_aarch64_2025-03-05.qcow2
#redis7_image_name=trove_redis7.0.15_kyv10sp3_aarch64_2025-03-05.qcow2
#postgresql13_image_name=trove_postgresql_13.3_2024-02-18.qcow2
#clickhouse_image_name=trove_********_clickhouse_22.5.1.2079_kylinv10sp3_aarch64_20250722.qcow2
#clickhousekeeper_image_name=trove_clickhousekeeper_kyv10sp3_aarch64_2025-05-30.qcow2

# 使用说明：
# 1. 行首有#号表示注释，不会上传
# 2. 去掉#号来启用对应镜像的上传
# 3. 镜像文件必须存在于 /data/img-mgmt/workspace/working/packer/pre-test/ 目录
# 4. 上传脚本会读取此配置文件中未注释的镜像进行批量上传

# 示例操作：
# 1. 确认构建完成的镜像位置
ls -la /data/img-mgmt/workspace/working/packer/pre-test/新镜像.qcow2

# 2. 编辑配置文件，添加或启用对应镜像
echo "mysql8037_image_name=新镜像.qcow2" >> /data/wait_to_upload.img.list.zwl

# 3. 执行上传
/data/upload_image_zwl.sh
```

### B. 常用命令速查

#### B.1 镜像操作命令
```bash
# 查看镜像信息
qemu-img info image.qcow2

# 转换镜像格式
qemu-img convert -f qcow2 -O raw image.qcow2 image.raw

# 压缩镜像
virt-sparsify --compress --format qcow2 source.qcow2 target.qcow2

# 查看镜像内文件
virt-ls -a image.qcow2 /etc/

# 读取镜像内文件内容
virt-cat -a image.qcow2 /etc/my.cnf
```

#### B.2 验证命令
```bash
# 检查Trove版本（从PKG-INFO文件）
virt-cat -a image.qcow2 /usr/lib/python2.7/site-packages/trove-*.egg-info/PKG-INFO | grep "Version:"

# 检查Trove版本（从METADATA文件，dist-info格式）
virt-ls -a image.qcow2 /usr/lib/python2.7/site-packages/ | grep "^trove-.*\.dist-info$"
virt-cat -a image.qcow2 /usr/lib/python2.7/site-packages/trove-*.dist-info/METADATA | grep "Version:"

# 检查Trove版本（从__init__.py文件）
virt-cat -a image.qcow2 /usr/lib/python2.7/site-packages/trove/__init__.py | grep "__version__"

# 检查服务状态
virt-customize -a image.qcow2 --run-command "systemctl status mysqld"

# 检查配置文件
virt-cat -a image.qcow2 /etc/trove/trove.conf
```

#### B.3 备用方案：virt-customize快速镜像生成
```bash
# 注意：这是Packer出现问题时的备用方案
# 适用于x86_64和aarch64环境，可快速生成镜像

# 硅谷ARM环境示例
# 1. 进入工作目录
cd /data/img-mgmt/workspace/working/virt-customize/

# 2. 快速构建脚本示例 (MySQL 5.7.34) - 备用方案
#!/bin/bash
# 当Packer构建失败时使用此方案
DATE=$(date +%Y%m%d)
TROVE_VERSION="10.5.1.dev5"
DB_TYPE="mysql"
DB_VERSION="5.7.34"

BASE_IMG="/data/img-mgmt/base/trove_mysql8.0.21_kyv10sp3_aarch64_2025-06-17_base.qcow2"
TROVE_PKG="/data/img-mgmt/workspace/archive/pip_bank/trove-${TROVE_VERSION}.tar.gz"
OUTPUT_IMG="/data/img-mgmt/workspace/working/virt-customize/trove_${TROVE_VERSION}_${DB_TYPE}_${DB_VERSION}_kyv10sp3_aarch64_${DATE}.qcow2"

# 检查基础镜像是否存在
if [ ! -f "$BASE_IMG" ]; then
    echo "错误: 基础镜像不存在: $BASE_IMG"
    exit 1
fi

# 检查Trove包是否存在
if [ ! -f "$TROVE_PKG" ]; then
    echo "错误: Trove包不存在: $TROVE_PKG"
    exit 1
fi

# 复制基础镜像
echo "复制基础镜像..."
cp "$BASE_IMG" "$OUTPUT_IMG"

# 使用virt-customize更新Trove (备用方案)
echo "使用virt-customize更新Trove到版本 $TROVE_VERSION..."
virt-customize -a "$OUTPUT_IMG" \
  --copy-in "$TROVE_PKG":/opt/ \
  --run-command "pip2 uninstall -y trove" \
  --run-command "pip2 install --no-compile /opt/trove-${TROVE_VERSION}.tar.gz" \
  --run-command "rm -f /opt/trove-${TROVE_VERSION}.tar.gz" \
  --run-command "sh /root/clean_history.sh" \
  --run-command "history -c"

# 压缩镜像
echo "压缩镜像..."
TEMP_IMG="${OUTPUT_IMG}.tmp"
virt-sparsify --compress --format qcow2 "$OUTPUT_IMG" "$TEMP_IMG"
mv "$TEMP_IMG" "$OUTPUT_IMG"

echo "备用方案构建完成: $OUTPUT_IMG"
qemu-img info "$OUTPUT_IMG"

# 3. 配置上传列表（使用镜像文件名，不是完整路径）
IMAGE_NAME=$(basename "$OUTPUT_IMG")
echo "mysql8037_image_name=$IMAGE_NAME" >> /data/wait_to_upload.img.list.zwl

# 注意：确保镜像在预期目录
cp "$OUTPUT_IMG" /data/img-mgmt/workspace/working/packer/pre-test/

# 4. 上传测试
/data/upload_image_zwl.sh

# 4. 验证镜像
virt-customize -a "$OUTPUT_IMG" --run-command "trove-api --version"
virt-cat -a "$OUTPUT_IMG" /etc/my.cnf | grep -i trove

# 使用场景：
# - Packer构建失败或超时
# - 需要快速修复镜像问题
# - 临时紧急镜像制作需求
# - 测试环境快速验证
```
